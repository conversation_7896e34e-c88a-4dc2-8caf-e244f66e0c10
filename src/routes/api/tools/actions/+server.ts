import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { pb } from '$lib/db/pocketbase';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const trackingData = await request.json();

        if (!trackingData.action) {
            return json({ error: 'Action is required' }, { status: 400 });
        }

        const savedRecord = await pb.create('tracking', trackingData);
        return json({
            success: true,
            message: 'Tracking data stored successfully',
            id: savedRecord.id
        });

    } catch (error) {
        console.error('Failed to store tracking data:', error);
        return json({ error: 'Failed to store tracking data' }, { status: 500 });
    }
};