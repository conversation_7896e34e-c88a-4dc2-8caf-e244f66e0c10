import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { pb } from '$lib/db/pocketbase';

export const POST: RequestHandler = async ({ request, locals }) => {
    try {
        const { userName, userEmail, userPassword } = locals.createUserBody;
        const userData = {
            email: userEmail,
            password: userPassword,
            passwordConfirm: userPassword,
            name: userName,
            emailVisibility: true
        };
        const user = await pb.create('users', userData);

        return json({
            success: true,
            message: 'Usuário criado com sucesso',
            userId: user.id
        });

    } catch (error: any) {
        console.error('❌ Erro ao criar usuário:', error);

        if (error?.data?.data) {
            const pbErrors = error.data.data;
            if (pbErrors.email) {
                return json({
                    success: false,
                    error: 'Este email já está em uso'
                }, { status: 400 });
            }
        }

        return json({
            success: false,
            error: 'Erro interno do servidor ao criar usuário'
        }, { status: 500 });
    }
};
