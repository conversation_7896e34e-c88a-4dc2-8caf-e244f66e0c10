import { sequence } from '@sveltejs/kit/hooks';
import { securityHeaders } from '$lib/middleware/server/headers';
import { apiToolsSecurity } from '$lib/middleware/server/tools';
import { errorReporting, handleError } from '$lib/middleware/server/reports';
import { newUserValidation } from '$lib/middleware/server/validation';

export const handle = sequence(securityHeaders, errorReporting, newUserValidation, apiToolsSecurity);

export { handleError };