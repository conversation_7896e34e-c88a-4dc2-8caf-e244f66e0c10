import type { <PERSON><PERSON> } from '@sveltejs/kit';
import { json } from '@sveltejs/kit';
import { z } from 'zod';
import { createUserSchema } from '$lib/zod/schemas';

export const newUserValidation: Handle = async ({ event, resolve }) => {
    const { url, request } = event;
    if (url.pathname === '/api/auth/create-user' && request.method === 'POST') {
        try {
            const body = await event.request.json();
            const validatedData = createUserSchema.parse({
                userName: "a",
                userEmail: "b",
            });
            event.locals.createUserBody = validatedData;
        } catch (error) {
            console.log('❌ Validação middleware falhou:', {
                path: event.url.pathname,
                error: error instanceof z.ZodError ? error.errors : error
            });

            if (error instanceof z.ZodError) {
                const fieldErrors: Record<string, string> = {};
                error.errors.forEach((err) => {
                    const field = err.path[0] as string;
                    fieldErrors[field] = err.message;
                });

                return json({
                    success: false,
                    error: 'Dad<PERSON> inválidos',
                    fieldErrors
                }, { status: 400 });
            }

            return json({
                success: false,
                error: 'Erro na validação dos dados'
            }, { status: 400 });
        }
    }

    return resolve(event);
};
