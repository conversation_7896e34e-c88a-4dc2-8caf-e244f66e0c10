import z from "zod";

export const baseRegistrationSchema = z.object({
    userName: z.string()
        .min(2, 'Nome deve ter pelo menos 2 caracteres')
        .max(50, 'Nome muito longo'),
    userEmail: z.string()
        .email('Digite um email válido'),
    userPassword: z.string()
        .min(8, 'Senha deve ter pelo menos 8 caracteres'),
    userConfirmPassword: z.string()
});

// Schema para criação de usuário (sem confirmação de senha)
export const createUserSchema = z.object({
    userName: z.string()
        .min(2, 'Nome deve ter pelo menos 2 caracteres')
        .max(50, 'Nome muito longo'),
    userEmail: z.string()
        .email('Digite um email válido'),
    userPassword: z.string()
        .min(8, 'Senha deve ter pelo menos 8 caracteres')
});

export const registrationSchema = baseRegistrationSchema.refine(
    data => data.userPassword === data.userConfirmPassword,
    {
        message: 'As senhas não coincidem',
        path: ['userConfirmPassword']
    }
);