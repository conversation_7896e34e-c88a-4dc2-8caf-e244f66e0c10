import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { browser } from '$app/environment';
import { pb } from '$lib/db/pocketbase';
import trackingService from '$lib/tools/tracking';
import reportsService from '$lib/tools/reports';
import z from "zod";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChild<T> = T extends { child?: any } ? Omit<T, "child"> : T;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type WithoutChildren<T> = T extends { children?: any } ? Omit<T, "children"> : T;
export type WithoutChildrenOrChild<T> = WithoutChildren<WithoutChild<T>>;
export type WithElementRef<T, U extends HTMLElement = HTMLElement> = T & { ref?: U | null };

export function formatDate(dateString: string): string {
	try {
		const date = new Date(dateString);
		return date.toLocaleString('pt-BR', {
			day: '2-digit',
			month: '2-digit',
			year: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	} catch {
		return dateString;
	}
}

export async function apiCall<T = any>(
	url: string,
	options: {
		method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
		body?: any;
		headers?: Record<string, string>;
		onSuccess?: (data: T) => void;
		onFieldErrors?: (errors: Record<string, string>) => void;
		onError?: (error: string) => void;
		trackAction?: string;
		functionName?: string;
	} = {}
): Promise<{
	success: boolean;
	data?: T;
	error?: string;
	fieldErrors?: Record<string, string>;
}> {
	const {
		method = 'GET',
		body,
		headers = {},
		onSuccess,
		onFieldErrors,
		onError,
		trackAction,
		functionName
	} = options;

	const defaultHeaders: Record<string, string> = {
		'Content-Type': 'application/json',
		...headers
	};
	const userId = browser && pb ? pb.getCurrentUser()?.id : undefined;

	try {
		const response = await fetch(url, {
			method,
			headers: defaultHeaders,
			body: body ? JSON.stringify(body) : undefined
		});
		const result = await response.json();

		if (trackAction && response.ok) {
			trackingService.trackAction(trackAction, {
				success: response.ok,
				status: response.status,
				url,
				method
			}, userId);
		}

		if (response.status === 400 && result.fieldErrors) {
			if (onFieldErrors) {
				onFieldErrors(result.fieldErrors);
			}
			if (onError && result.error) {
				onError(result.error);
			}
			return {
				success: false,
				fieldErrors: result.fieldErrors,
				error: result.error
			};
		}

		if (!response.ok || !result.success) {
			const errorMessage = result.error || `Erro ${response.status}`;

			if (response.status >= 500) {
				await reportsService.report(
					'API Error',
					`${method} ${url} failed with status ${response.status}`,
					'critical',
					{
						functionName,
						userId,
						metadata: {
							status: response.status,
							url,
							method,
							responseBody: result
						}
					}
				);
			}

			if (onError) {
				onError(errorMessage);
			}

			return {
				success: false,
				error: errorMessage
			};
		}

		if (onSuccess && result.data) {
			onSuccess(result.data);
		}

		return {
			success: true,
			data: result.data
		};

	} catch (error: any) {
		const errorMessage = 'Erro de conexão';
		await reportsService.report(
			'Network Error',
			`${method} ${url} failed: ${error.message}`,
			'high',
			{
				functionName,
				userId,
				error,
				metadata: {
					url,
					method
				}
			}
		);

		if (trackAction) {
			trackingService.trackAction(trackAction, {
				success: false,
				error: 'network_error',
				url,
				method,
				errorMessage: error.message
			}, userId);
		}

		if (onError) {
			onError(errorMessage);
		}

		return {
			success: false,
			error: errorMessage
		};
	}
}

export const handleZodErrors = (error: any, state: { fieldErrors: Record<string, string>; errorMessage: string }) => {
	if (error instanceof z.ZodError) {
		error.errors.forEach((err) => {
			const field = err.path[0] as string;
			state.fieldErrors[field] = err.message;
		});
		state.fieldErrors = { ...state.fieldErrors };
		state.errorMessage = 'Corrija os erros abaixo para continuar';
	}
};