<script lang="ts">
	import { globalState } from '$lib/states/global.state.svelte';
	import Button from '../ui/button/button.svelte';
	import Icon from '../ui/icon.svelte';
	import { scale, fly } from 'svelte/transition';

	let visible = $state(false);

	$effect(() => {
		visible = true;
	});

	const achievements = [
		{
			number: '6+',
			text: 'Anos de experiência',
			icon: 'lucide:calendar-days'
		},
		{
			number: '500+',
			text: 'Pessoas transformadas',
			icon: 'lucide:users'
		},
		{
			number: '100%',
			text: 'Treinos de qualidade',
			icon: 'lucide:target'
		}
	];

	const specialties = [
		{
			title: 'Treino em Casa',
			description: 'Especialista em exercícios para qualquer ambiente',
			icon: 'lucide:home'
		},
		{
			title: 'CrossFit & Ginástica',
			description: 'Técnicas avançadas de crossfit e movimentos ginásticos',
			icon: 'lucide:dumbbell'
		},
		{
			title: 'Grupos Especiais',
			description: 'Experiência com grávidas, idosos e todas as idades',
			icon: 'lucide:heart-handshake'
		},
		{
			title: 'Atletas de Elite',
			description: 'Treinou atletas de crossfit de alto rendimento',
			icon: 'lucide:trophy'
		}
	];
</script>

<section class="relative mx-auto max-w-[1200px] px-6 py-24">
	<!-- Section Header -->
	<div class="mb-16 text-center">
		<div class="mb-4 flex justify-center">
			<div
				class="from-accent/10 to-primary/10 inline-flex items-center gap-2 rounded-full bg-gradient-to-r px-4 py-2 text-sm font-medium"
			>
				<Icon icon="lucide:star" class="text-primary h-4 w-4" />
				<span class="text-foreground">Conheça sua personal trainer</span>
			</div>
		</div>

		<h2 class="font-heading text-foreground mb-6 text-3xl font-bold md:text-4xl lg:text-5xl">
			Uma
			<span
				class="from-primary via-primary to-accent bg-gradient-to-r bg-clip-text text-transparent"
			>
				profissional excepcional
			</span>
			que vai te levar ao seu objetivo
		</h2>

		<p class="text-muted-foreground mx-auto max-w-2xl text-lg leading-relaxed md:text-xl">
			Com mais de 6 anos transformando vidas através do movimento, ela combina experiência técnica
			com uma abordagem personalizada para cada aluno.
		</p>
	</div>

	<div class="grid gap-16 lg:grid-cols-2 lg:items-center">
		<!-- Professional Image -->
		<div class="relative">
			{#if visible}
				<div class="relative overflow-hidden rounded-2xl" in:scale={{ duration: 600, delay: 200 }}>
					<!-- Placeholder for professional image -->
					<div
						class="from-primary/20 to-accent/20 flex aspect-[4/5] w-full items-center justify-center overflow-hidden bg-gradient-to-br"
					>
						<img
							src="https://scontent.cdninstagram.com/v/t51.71878-15/502245361_686633080964524_2030230228087084472_n.jpg?stp=dst-jpg_e15_tt6&_nc_cat=105&ig_cache_key=MzMyMTY2ODA0ODMxNzMzMzk4OQ%3D%3D.3-ccb1-7&ccb=1-7&_nc_sid=58cdad&efg=eyJ2ZW5jb2RlX3RhZyI6InhwaWRzLjExMjR4MjAwMC5zZHIifQ%3D%3D&_nc_ohc=3HNMWCddwjAQ7kNvwEsOC0_&_nc_oc=AdnrqnMBahyP-GatW-zFTcAy8riucoRe7ZySsr35VgSeMwYY7qqpxBnDu3c1P8EZliWrLUjKA8idJkjC8oXc-s4h&_nc_ad=z-m&_nc_cid=0&_nc_zt=23&_nc_ht=scontent.cdninstagram.com&_nc_gid=zJl1UkQwC-eQq17d4rBuug&oh=00_AfNedLehXNFnisPFLc1AIktjbfel8Hg7mT6ELOUSmzbeMw&oe=6854E34F"
							alt="Foto de Flavia Silva"
							class="object-cover"
						/>
					</div>

					<!-- Achievement badges overlay -->
					<div class="absolute -right-4 bottom-8 space-y-4 lg:top-8">
						{#each achievements as achievement, i}
							<div
								class="bg-background/90 border-border/50 rounded-xl border p-4 shadow-lg backdrop-blur-3xl"
								in:fly={{ x: 50, duration: 400, delay: 400 + i * 100 }}
							>
								<div class="flex items-center gap-3">
									<div class="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-lg">
										<Icon icon={achievement.icon} class="text-primary h-5 w-5" />
									</div>
									<div>
										<div class="text-foreground text-xl font-bold">{achievement.number}</div>
										<div class="text-muted-foreground text-xs">{achievement.text}</div>
									</div>
								</div>
							</div>
						{/each}
					</div>
				</div>
			{/if}
		</div>

		<!-- Content -->
		<div class="space-y-8">
			{#if visible}
				<div in:fly={{ y: 30, duration: 600, delay: 300 }}>
					<h3 class="font-heading text-foreground mb-4 text-2xl font-bold md:text-3xl">
						Mais que uma personal trainer:
						<span class="from-primary to-accent bg-gradient-to-r bg-clip-text text-transparent">
							uma parceira na sua jornada
						</span>
					</h3>

					<div class="text-muted-foreground space-y-4 text-lg leading-relaxed">
						<p>
							<strong class="text-foreground">6 anos</strong> dedicados inteiramente ao mundo
							fitness. Começou treinando
							<strong class="text-foreground">atletas de crossfit de alto rendimento</strong>, onde
							aprendeu as técnicas mais avançadas de condicionamento físico e movimentos ginásticos.
						</p>

						<p>
							Sua experiência única inclui trabalho especializado com
							<strong class="text-foreground">grupos especiais como grávidas</strong> e pessoas de
							<strong class="text-foreground">todas as idades</strong>, desenvolvendo uma
							sensibilidade especial para adaptar treinos às necessidades individuais de cada
							pessoa.
						</p>

						<p>
							Hoje, é reconhecida como
							<strong class="text-foreground">especialista em treinos para casa</strong>, criando
							metodologias que garantem resultados efetivos independente do espaço ou equipamentos
							disponíveis.
						</p>
					</div>
				</div>

				<!-- Specialties Grid -->
				<div class="grid gap-4 sm:grid-cols-2" in:fly={{ y: 30, duration: 600, delay: 500 }}>
					{#each specialties as specialty, i}
						<div
							class="border-border/50 bg-card/50 hover:bg-card/80 group hover:border-primary/20 rounded-xl border p-4 backdrop-blur-sm transition-all duration-300 hover:shadow-lg"
							in:scale={{ duration: 400, delay: 600 + i * 100 }}
						>
							<div class="flex items-start gap-3">
								<div
									class="bg-primary/10 group-hover:bg-primary/20 flex h-10 w-10 shrink-0 items-center justify-center rounded-lg transition-colors"
								>
									<Icon icon={specialty.icon} class="text-primary h-5 w-5" />
								</div>
								<div>
									<h4 class="text-foreground mb-1 font-semibold">{specialty.title}</h4>
									<p class="text-muted-foreground text-sm leading-relaxed">
										{specialty.description}
									</p>
								</div>
							</div>
						</div>
					{/each}
				</div>

				<!-- Call to Action -->
				<div class="pt-4" in:fly={{ y: 20, duration: 600, delay: 800 }}>
					<div
						class="from-primary/5 to-accent/5 border-primary/10 rounded-xl border bg-gradient-to-r p-6"
					>
						<div class="text-center">
							<h4 class="text-foreground mb-2 text-lg font-semibold">
								Pronta para começar sua transformação?
							</h4>
							<p class="text-muted-foreground mb-4 text-sm">
								Junte-se a centenas de pessoas que já alcançaram seus objetivos com nossa
								metodologia comprovada.
							</p>
							<Button variant="default" size="default" class="gap-2" href="/preview" preload>
								<Icon icon="lucide:play-circle" class="h-4 w-4" />
								Começar minha jornada
							</Button>
						</div>
					</div>
				</div>
			{/if}
		</div>
	</div>

	<!-- Quote Section -->
	{#if visible}
		<div class="mt-20 text-center" in:fly={{ y: 30, duration: 600, delay: 1000 }}>
			<div
				class="from-primary/5 to-accent/5 border-primary/10 mx-auto max-w-4xl rounded-2xl border bg-gradient-to-r p-8 md:p-12"
			>
				<Icon icon="lucide:quote" class="text-primary/20 mx-auto mb-6 text-3xl" />
				<blockquote class="text-foreground mb-6 text-xl font-medium italic md:text-2xl">
					"Minha missão vai além de ensinar exercícios. É sobre empoderar cada pessoa a descobrir
					sua força interior, construir confiança e criar uma relação saudável com o movimento que
					dure para toda a vida."
				</blockquote>
				<div class="flex items-center justify-center gap-3">
					<div class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">
						<Icon icon="lucide:user" class="text-primary text-xl" />
					</div>
					<div class="text-left">
						<div class="text-foreground font-semibold">Flavia Silva — Sua Personal Trainer</div>
						<div class="text-muted-foreground text-sm">Especialista em Fitness & Bem-estar</div>
					</div>
				</div>
			</div>
		</div>
	{/if}
</section>
