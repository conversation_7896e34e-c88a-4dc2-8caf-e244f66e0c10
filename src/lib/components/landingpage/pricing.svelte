<script lang="ts">
	import { globalState } from '$lib/states/global.state.svelte';
	import Button from '../ui/button/button.svelte';
	import Icon from '../ui/icon.svelte';
	import { scale, fly } from 'svelte/transition';

	let selectedPlan = $state('monthly');

	const plans = {
		monthly: {
			id: 'monthly',
			title: 'Mensal',
			price: 50,
			originalPrice: 50,
			discount: 0,
			icon: 'lucide:play-circle',
			features: [
				'Acesso a todos os treinos diários',
				'Novos exercícios toda semana',
				'Suporte via comunidade',
				'Acesso pelo celular e computador'
			],
			cta: 'Começar agora',
			popular: false
		},
		quarterly: {
			id: 'quarterly',
			title: 'Trimestral',
			price: 40,
			originalPrice: 50,
			discount: 20,
			icon: 'lucide:video',
			features: [
				'Acesso a todos os treinos diários',
				'Novos exercícios toda semana',
				'Suporte via comunidade',
				'Acesso pelo celular e computador',
				'Economia de 20% no valor mensal'
			],
			cta: 'Melhor custo-benefício',
			popular: true
		}
	};
</script>

<section class="relative mx-auto max-w-[1200px] px-6 py-24">
	<div class="mb-16 text-center">
		<h2 class="font-heading text-foreground mb-4 text-3xl font-bold md:text-4xl lg:text-5xl">
			Invista em você com
			<span
				class="from-primary via-primary to-accent bg-gradient-to-r bg-clip-text text-transparent"
			>
				planos acessíveis
			</span>
		</h2>
		<p class="text-muted-foreground mx-auto max-w-2xl text-lg">
			Escolha o plano ideal para sua jornada de transformação física e comece a treinar hoje mesmo.
		</p>
	</div>

	<!-- Pricing Cards -->
	<div class="grid gap-8 md:grid-cols-2 md:gap-6 lg:gap-8">
		{#each Object.values(plans) as plan}
			<div
				class="relative flex flex-col justify-between overflow-hidden rounded-2xl border p-8 transition-all duration-300 {plan.popular
					? 'border-primary/50 bg-primary/5 shadow-xl'
					: 'border-border bg-card/50'}"
				in:scale={{ duration: 400, delay: plan.id === 'monthly' ? 100 : 200 }}
			>
				<!-- Popular Badge -->
				{#if plan.popular}
					<div class="absolute top-0 right-0">
						<div
							class="bg-primary text-primary-foreground mt-5 -mr-12 rotate-45 px-12 py-1 text-xs font-medium"
						>
							Recomendado
						</div>
					</div>
				{/if}

				<!-- Card Content -->
				<div>
					<!-- Header -->
					<div class="mb-6 flex items-center gap-4">
						<div
							class="{plan.popular
								? 'bg-primary/20 text-primary'
								: 'bg-muted/50 text-muted-foreground'} flex h-12 w-12 items-center justify-center rounded-xl"
						>
							<Icon icon={plan.icon} />
						</div>
						<h3 class="font-heading text-foreground text-xl font-bold">{plan.title}</h3>
					</div>

					<!-- Price -->
					<div class="mb-6">
						<div class="flex items-end gap-2">
							<span class="font-heading text-foreground text-4xl font-bold">R$ {plan.price}</span>
							<span class="text-muted-foreground mb-1 text-lg">/mês</span>
						</div>

						{#if plan.discount > 0}
							<div class="mt-2 flex items-center gap-2">
								<span class="text-muted-foreground line-through">R$ {plan.originalPrice}/mês</span>
								<span class="bg-accent/10 text-accent rounded-full px-2 py-0.5 text-xs font-medium">
									{plan.discount}% OFF
								</span>
							</div>
						{/if}
					</div>

					<!-- Features -->
					<ul class="mb-8 space-y-3">
						{#each plan.features as feature}
							<li class="flex items-start gap-3">
								<Icon icon="lucide:check-circle" class="text-primary mt-0.5 h-5 w-5" />
								<span class="text-muted-foreground">{feature}</span>
							</li>
						{/each}
					</ul>

					<!-- CTA -->
				</div>
				<Button
					size="lg"
					variant={plan.popular ? 'default' : 'outline'}
					class="w-full justify-center shadow-lg"
					href="/preview"
					preload
				>
					<Icon icon="lucide:zap" />
					{plan.cta}s
				</Button>
			</div>
		{/each}
	</div>

	<!-- Background Decorative Elements -->
	<div class="absolute inset-0 -z-10 overflow-hidden">
		<!-- Grid Pattern -->
		<div class="absolute inset-0 opacity-30">
			<div
				class="via-primary/5 h-full w-full bg-gradient-to-br [background-image:radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)] from-transparent to-transparent [background-size:20px_20px]"
			></div>
		</div>
	</div>
</section>
