<script lang="ts">
	import * as Card from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import { Progress } from '$lib/components/ui/progress';
	import { Badge } from '$lib/components/ui/badge';
	import { Separator } from '$lib/components/ui/separator';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import Icon from '$lib/components/ui/icon.svelte';
	import { cn } from '$lib/utils';
	import { AuthState } from '$lib/states/auth.state.svelte';

	const authState = new AuthState();
	let currentStep = $state(1);
	let selectedObjective = $state('');
	let selectedExperience = $state('');

	const totalSteps = 3;
	const objectives = [
		{ id: 'lose_weight', label: '🔥 Queimar gordura e ficar em forma', icon: 'mdi:target' },
		{ id: 'gain_muscle', label: '💪 Ganhar massa muscular', icon: 'mdi:dumbbell' },
		{ id: 'get_fit', label: '⚡ Melhorar condicionamento físico', icon: 'mdi:heart-pulse' },
		{ id: 'maintain', label: '🛡️ Manter minha forma atual', icon: 'mdi:shield-check' }
	];
	const experiences = [
		{
			id: 'beginner',
			label: '🌱 Iniciante',
			description: 'Estou começando minha jornada fitness agora!',
			icon: 'mdi:sprout'
		},
		{
			id: 'intermediate',
			label: '🌳 Intermediário',
			description: 'Já tenho alguma experiência e quero continuar evoluindo',
			icon: 'mdi:tree'
		},
		{
			id: 'advanced',
			label: '🏆 Avançado',
			description: 'Treino regularmente e busco sempre novos desafios',
			icon: 'mdi:pine-tree'
		}
	];

	const videoId = 'dQw4w9WgXcQ'; // Substitua pelo video real

	const progressValue = $derived((currentStep / totalSteps) * 100);

	function nextStep() {
		if (currentStep < totalSteps) {
			currentStep += 1;
		}
	}

	function prevStep() {
		if (currentStep > 1) {
			currentStep -= 1;
		}
	}

	function selectObjective(objectiveId: string) {
		selectedObjective = objectiveId;
		setTimeout(nextStep, 300);
	}

	function selectExperience(experienceId: string) {
		selectedExperience = experienceId;
		setTimeout(nextStep, 300);
	}

	function createUnifiedHandler(action: () => void) {
		let handled = false;
		let timeoutId: ReturnType<typeof setTimeout>;

		return (event: Event) => {
			event.preventDefault();
			event.stopPropagation();

			if (handled) return;
			handled = true;

			action();

			clearTimeout(timeoutId);
			timeoutId = setTimeout(() => {
				handled = false;
			}, 500);
		};
	}

	async function handleSubmit() {
		const result = await authState.handleSignup();
		if (result.success) {
			const onboardingData = {
				objective: selectedObjective,
				experience: selectedExperience,
				timestamp: new Date().toISOString()
			};
			localStorage.setItem('onboardingData', JSON.stringify(onboardingData));

			console.log('Onboarding data stored:', onboardingData);
		}
	}

	async function handleGoogleAuth() {
		const result = await authState.handleGoogleAuth();
		if (result.success) {
			const onboardingData = {
				objective: selectedObjective,
				experience: selectedExperience,
				timestamp: new Date().toISOString()
			};
			localStorage.setItem('onboardingData', JSON.stringify(onboardingData));

			console.log('Google Auth with onboarding data:', onboardingData);
		}
	}
</script>

<div class="flex min-h-screen items-center justify-center pt-2 pb-5 lg:h-screen lg:py-0">
	<Card.Root class="mx-auto w-full max-w-5xl overflow-hidden">
		<div class="flex min-h-[600px] flex-col lg:flex-row">
			<div class="flex flex-1 flex-col">
				<Card.Header class="space-y-6 p-6 lg:p-8">
					<div class="flex items-center justify-between">
						<div class="space-y-1">
							<Card.Title class="text-2xl lg:text-3xl">
								{#if currentStep === 1}
									Qual é o seu grande objetivo? 🎯
								{:else if currentStep === 2}
									Em que nível você está agora?
								{:else}
									Você está a 1 passo da transformação! 🚀
								{/if}
							</Card.Title>
							<Card.Description class="text-base lg:text-lg">
								{#if currentStep === 1}
									Conte-nos qual é seu sonho fitness e vamos te ajudar a CONQUISTAR seus objetivos!
								{:else if currentStep === 2}
									Queremos te conhecer melhor para criar a melhor experiência possível para VOCÊ
								{:else}
									Crie sua conta GRÁTIS e comece sua jornada de transformação hoje mesmo!
								{/if}
							</Card.Description>
						</div>
						<Badge variant="outline">
							{currentStep}/{totalSteps}
						</Badge>
					</div>

					<div class="space-y-2">
						<Progress value={progressValue} class="h-2" />
						<div class="text-muted-foreground flex justify-between text-sm">
							<span>Etapa {currentStep} de {totalSteps}</span>
							<span>{Math.round(progressValue)}% completo</span>
						</div>
					</div>
				</Card.Header>

				<Separator />

				<Card.Content class="flex-1 p-6 lg:p-8">
					{#if authState.errorMessage}
						<Alert variant="destructive" class="mb-6">
							<Icon icon="alert-circle" class="h-4 w-4" />
							<AlertDescription>{authState.errorMessage}</AlertDescription>
						</Alert>
					{/if}

					{#if currentStep === 1}
						<div class="space-y-4">
							{#each objectives as objective}
								<button
									type="button"
									class={cn(
										'hover:border-primary hover:bg-primary/5 w-full cursor-pointer touch-manipulation rounded-lg border-2 p-4 text-left transition-all',
										selectedObjective === objective.id
											? 'border-primary bg-primary/10'
											: 'border-border'
									)}
									onclick={createUnifiedHandler(() => selectObjective(objective.id))}
									tabindex="0"
									onkeydown={(e) => {
										if (e.key === 'Enter' || e.key === ' ') {
											e.preventDefault();
											selectObjective(objective.id);
										}
									}}
								>
									<div class="flex items-center space-x-3">
										<span class="font-medium">{objective.label}</span>
									</div>
								</button>
							{/each}
						</div>
					{:else if currentStep === 2}
						<div class="space-y-4">
							{#each experiences as experience}
								<button
									type="button"
									class={cn(
										'hover:border-primary hover:bg-primary/5 w-full cursor-pointer touch-manipulation rounded-lg border-2 p-4 text-left transition-all',
										selectedExperience === experience.id
											? 'border-primary bg-primary/10'
											: 'border-border'
									)}
									onclick={createUnifiedHandler(() => selectExperience(experience.id))}
									tabindex="0"
									onkeydown={(e) => {
										if (e.key === 'Enter' || e.key === ' ') {
											e.preventDefault();
											selectExperience(experience.id);
										}
									}}
								>
									<div class="flex items-start space-x-3">
										<div class="space-y-1">
											<h4 class="font-medium">{experience.label}</h4>
											<p class="text-muted-foreground text-sm">{experience.description}</p>
										</div>
									</div>
								</button>
							{/each}
						</div>
					{:else}
						<div class="space-y-6">
							<Button
								variant="outline"
								size="lg"
								class="h-12 w-full"
								disabled={authState.isLoading}
								onclick={handleGoogleAuth}
							>
								{#if authState.isLoading}
									<Icon icon="loader-2" class="mr-2 h-4 w-4 animate-spin" />
								{:else}
									<svg class="mr-2 h-5 w-5" viewBox="0 0 24 24">
										<path
											fill="#4285F4"
											d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
										/>
										<path
											fill="#34A853"
											d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
										/>
										<path
											fill="#FBBC05"
											d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
										/>
										<path
											fill="#EA4335"
											d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
										/>
									</svg>
								{/if}
								⚡ Começar RÁPIDO com Google
							</Button>

							<div class="relative">
								<Separator />
								<span
									class="bg-background text-muted-foreground absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 px-2 text-xs"
								>
									ou criar conta manual
								</span>
							</div>

							<form onsubmit={handleSubmit} class="space-y-4">
								<div class="space-y-2">
									<Label for="name">Nome completo</Label>
									<Input
										id="name"
										type="text"
										placeholder="Como podemos te chamar?"
										bind:value={authState.userName}
										disabled={authState.isLoading}
										class={authState.fieldErrors.userName ? 'border-red-500' : ''}
										onblur={() => authState.validateFieldOnBlur('userName', authState.userName)}
									/>
									{#if authState.fieldErrors.userName}
										<p class="text-sm text-red-500">{authState.fieldErrors.userName}</p>
									{/if}
								</div>

								<div class="space-y-2">
									<Label for="email">Email</Label>
									<Input
										id="email"
										type="email"
										placeholder="<EMAIL>"
										bind:value={authState.userEmail}
										disabled={authState.isLoading}
										class={authState.fieldErrors.userEmail ? 'border-red-500' : ''}
										onblur={() => authState.validateFieldOnBlur('userEmail', authState.userEmail)}
									/>
									{#if authState.fieldErrors.userEmail}
										<p class="text-sm text-red-500">{authState.fieldErrors.userEmail}</p>
									{/if}
								</div>

								<div class="space-y-2">
									<Label for="password">Senha</Label>
									<Input
										id="password"
										type="password"
										placeholder="Crie uma senha forte (mín. 8 caracteres)"
										bind:value={authState.userPassword}
										disabled={authState.isLoading}
										class={authState.fieldErrors.userPassword ? 'border-red-500' : ''}
										onblur={() =>
											authState.validateFieldOnBlur('userPassword', authState.userPassword)}
									/>
									{#if authState.fieldErrors.userPassword}
										<p class="text-sm text-red-500">{authState.fieldErrors.userPassword}</p>
									{/if}
								</div>

								<div class="space-y-2">
									<Label for="confirm-password">Confirmar senha</Label>
									<Input
										id="confirm-password"
										type="password"
										placeholder="Digite a senha novamente"
										bind:value={authState.userConfirmPassword}
										disabled={authState.isLoading}
										class={authState.fieldErrors.userConfirmPassword ? 'border-red-500' : ''}
										onblur={() =>
											authState.validateFieldOnBlur(
												'userConfirmPassword',
												authState.userConfirmPassword
											)}
									/>
									{#if authState.fieldErrors.userConfirmPassword}
										<p class="text-sm text-red-500">{authState.fieldErrors.userConfirmPassword}</p>
									{/if}
								</div>

								<Button type="submit" size="lg" class="h-12 w-full" disabled={authState.isLoading}>
									{#if authState.isLoading}
										<Icon icon="loader-2" class="mr-2 animate-spin text-lg" />
										Criando sua conta...
									{:else}
										<Icon icon="zap" class="mr-2 text-lg" />
										🚀 COMEÇAR MINHA TRANSFORMAÇÃO!
									{/if}
								</Button>
							</form>
						</div>
					{/if}
				</Card.Content>

				<!-- Footer com navegação -->
				{#if currentStep > 1 && currentStep < 3}
					<Separator />
					<Card.Footer class="p-6 lg:p-8">
						<div class="flex w-full justify-between">
							<Button variant="ghost" onclick={prevStep}>
								<Icon icon="mdi:arrow-left" class="mr-2 text-lg" />
								Voltar
							</Button>
							<Button onclick={nextStep}>
								Próximo
								<Icon icon="mdi:arrow-right" class="ml-2 text-lg" />
							</Button>
						</div>
					</Card.Footer>
				{/if}
			</div>

			<div class="order-first w-full px-5 lg:order-last lg:w-2/5 lg:px-0">
				<div
					class="relative h-64 min-h-[100px] overflow-hidden rounded-xl lg:h-full lg:min-h-0 lg:rounded-l-2xl"
				>
					<iframe
						src="https://www.youtube.com/embed/{videoId}?autoplay=1&mute=1&loop=1&playlist={videoId}&controls=0&showinfo=0&rel=0&end=30"
						class="h-full w-full object-cover"
						frameborder="0"
						allow="autoplay; encrypted-media"
						allowfullscreen
						title="Preview do treino"
					></iframe>

					<div class="absolute top-4 left-4">
						<Badge variant="secondary" class="border-0 bg-black/70 text-white">
							<Icon icon="play-circle" class="mr-1 h-3 w-3" />
							Preview
						</Badge>
					</div>
				</div>
			</div>
		</div>
	</Card.Root>
</div>
