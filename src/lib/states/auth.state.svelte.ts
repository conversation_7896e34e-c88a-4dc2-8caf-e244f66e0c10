import { z } from 'zod';
import { registrationSchema, baseRegistrationSchema } from '$lib/zod/schemas';
import { pb } from '$lib/db/pocketbase';
import { browser } from '$app/environment';
import { apiCall, handleZodErrors } from '$lib/utils';

export class AuthState {
    isLoading = $state(false);
    errorMessage = $state('');
    fieldErrors = $state<Record<string, string>>({});
    userName = $state('');
    userEmail = $state('');
    userPassword = $state('');
    userConfirmPassword = $state('');
    forgotEmail = $state('');

    private loginSchema = z.object({
        userEmail: z.string().email('Digite um email válido'),
        userPassword: z.string().min(1, 'Digite sua senha')
    });

    private forgotPasswordSchema = z.object({
        forgotEmail: z.string().email('Digite um email válido')
    });

    validateFieldOnBlur(fieldName: string, value: string) {
        try {
            if (fieldName === 'userConfirmPassword') {
                registrationSchema.parse({
                    userName: this.userName,
                    userEmail: this.userEmail,
                    userPassword: this.userPassword,
                    userConfirmPassword: this.userConfirmPassword
                });
            } else if (fieldName === 'forgotEmail') {
                this.forgotPasswordSchema.shape.forgotEmail.parse(value);
            } else if (['userName', 'userEmail', 'userPassword'].includes(fieldName)) {
                const zodFieldName = fieldName as keyof typeof baseRegistrationSchema.shape;
                baseRegistrationSchema.shape[zodFieldName].parse(value);
            } else if (['userEmail', 'userPassword'].includes(fieldName)) {
                this.loginSchema.shape[fieldName as keyof typeof this.loginSchema.shape].parse(value);
            }

            if (this.fieldErrors[fieldName]) {
                delete this.fieldErrors[fieldName];
                this.fieldErrors = { ...this.fieldErrors };
            }
        } catch {
        }
    }

    clearForms() {
        this.userName = '';
        this.userEmail = '';
        this.userPassword = '';
        this.userConfirmPassword = '';
        this.forgotEmail = '';
        this.fieldErrors = {};
    }

    clearMessages() {
        this.errorMessage = '';
        this.fieldErrors = {};
    }

    async handleLogin() {
        try {
            this.fieldErrors = {};
            this.errorMessage = '';

            const validData = this.loginSchema.parse({
                userEmail: this.userEmail,
                userPassword: this.userPassword
            });

            this.isLoading = true;

            if (!browser || !pb) {
                throw new Error('PocketBase não está disponível');
            }

            await pb.getClient().collection('users').authWithPassword(
                validData.userEmail,
                validData.userPassword
            );

            if (pb.isUserAuthenticated()) {
                setTimeout(() => {
                    window.location.href = '/app';
                }, 500);

                return { success: true };
            } else {
                throw new Error('Falha na autenticação');
            }

        } catch (error: any) {
            handleZodErrors(error, this);
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    async handleSignup() {
        try {
            this.fieldErrors = {};
            this.errorMessage = '';

            const validData = registrationSchema.parse({
                userName: this.userName,
                userEmail: this.userEmail,
                userPassword: this.userPassword,
                userConfirmPassword: this.userConfirmPassword
            });

            this.isLoading = true;

            const result = await apiCall('/api/auth/create-user', {
                method: 'POST',
                body: {
                    userName: validData.userName,
                    userEmail: validData.userEmail,
                    userPassword: validData.userPassword
                },
                trackAction: 'user_signup_attempt',
                functionName: 'AuthState.handleSignup',
                onFieldErrors: (errors) => {
                    this.fieldErrors = { ...errors };
                },
                onError: (error) => {
                    this.errorMessage = error;
                }
            });

            if (result.success) {
                if (browser && pb) {
                    await pb.getClient().collection('users').authWithPassword(
                        validData.userEmail,
                        validData.userPassword
                    );
                }

                setTimeout(() => {
                    window.location.href = '/app';
                }, 500);

                return { success: true };
            } else {
                if (Object.keys(this.fieldErrors).length > 0) {
                    this.errorMessage = 'Corrija os erros abaixo para continuar';
                }
                return { success: false };
            }

        } catch (error: any) {
            handleZodErrors(error, this);
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    async handleGoogleAuth() {
        this.clearMessages();
        this.isLoading = true;

        try {
            // Login com Google usando PocketBase
            if (!browser || !pb) {
                throw new Error('PocketBase não está disponível');
            }

            await pb.loginWithGoogle();

            // Verificar se o login foi bem-sucedido
            if (pb.isUserAuthenticated()) {
                // Redirect to blob on Google auth success
                setTimeout(() => {
                    window.location.href = '/blob';
                }, 500);

                return { success: true };
            } else {
                throw new Error('Falha na autenticação com Google');
            }

        } catch (error: any) {
            console.error('Erro na autenticação Google:', error);
            this.errorMessage = 'Erro na autenticação com Google. Tente novamente.';
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    async handleForgotPassword() {
        try {
            this.fieldErrors = {};
            this.errorMessage = '';

            const validData = this.forgotPasswordSchema.parse({
                forgotEmail: this.forgotEmail
            });

            this.isLoading = true;

            if (!browser || !pb) {
                throw new Error('PocketBase não está disponível');
            }

            await pb.getClient().collection('users').requestPasswordReset(validData.forgotEmail);

            return { success: true, message: 'Email de recuperação enviado! Verifique sua caixa de entrada.' };

        } catch (error: any) {
            if (error instanceof z.ZodError) {
                error.errors.forEach((err) => {
                    const field = err.path[0] as string;
                    this.fieldErrors[field] = err.message;
                });
                this.fieldErrors = { ...this.fieldErrors };
                this.errorMessage = 'Corrija os erros abaixo para continuar';
            } else {
                console.error('Erro no forgot password:', error);
                this.errorMessage = 'Erro ao enviar email de recuperação. Tente novamente.';
            }
            return { success: false };
        } finally {
            this.isLoading = false;
        }
    }

    isAuthenticated(): boolean {
        return browser && pb ? pb.isUserAuthenticated() : false;
    }

    getCurrentUser() {
        return browser && pb ? pb.getCurrentUser() : null;
    }

    async logout() {
        if (browser && pb) {
            pb.logoutUser();
            window.location.href = '/';
        }
    }
}

export const authState = new AuthState();